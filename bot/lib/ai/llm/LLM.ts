import { AIMessage, BaseMessage, HumanMessage, SystemMessage } from '@langchain/core/messages'
import { Config } from '../../../config/config'
import { StringOutputParser } from '@langchain/core/output_parsers'
import { IterableReadableStream } from '@langchain/core/dist/utils/stream'
import { AzureOpenAIClient, CheapOpenAI, OpenAIClient, QwenMax } from './client'
import { z } from 'zod'
import { Runnable, RunnableConfig } from '@langchain/core/runnables'
import logger from '../../../model/logger/logger'
import { StructuredTool } from '@langchain/core/tools'
import { StringHelper } from '../../string'
import { ChatPromptTemplate, ParamsFromFString, SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { randomSleep } from '../../schedule/schedule'
import { MessageSender } from '../../../service/moer/components/message/message_send'
import { IWecomMsgType } from '../../juzi/type'
import { PathHelper } from '../../path'
import { catchError } from '../../error/catchError'
import { FileHelper } from '../../file'

type ZodFunctionDef<Parameters = any> = {
    /** Function name. */
    name: string
    /** A description of what the function does. */
    description: string
    /** Zod schema defining the function's parameters, to convert to JSON schema. */
    schema: z.ZodType<Parameters>
};

interface LLMInitParams {
  model?: string
  temperature?: number
  maxTokens?: number
  responseJSON?: boolean  // 若设置为 true，系统提示词中必须包含 JSON 字样，如：请严格按照如下 JSON 格式输出
  systemPrompt?: string
  runnableConfig?: RunnableConfig
  promptName?: string
  meta?: Record<string, any>  // 用于 langSmith 记录元信息，方便查询，round_id 对应了 run_id， chat_id 对应了 thread_id
  projectName?: string  // 用于配置 langSmith 日志记录到的项目
}

enum ClientType {
  AZURE = 'azure',
  OPENAI = 'openai',
  CHEAP = 'cheap',
  QWEN = 'qwen'
}

export type ToolCall = {
  name: string;
  args: Record<string, any>;
  id?: string;
  type?: 'tool_call'
}

/**
 * OpenAI API 封装
 * 注意：
 * 1. 聊天记录需自己维护
 * 2. 尽量不要引入 openai 包，所有功能尽量以 LangChain 实现，保证稳定性
 */
export class LLM {
  private readonly model: string
  private readonly temperature: number
  private readonly maxTokens: number
  private readonly responseJSON: boolean | undefined
  private readonly systemPrompt?: string
  private readonly runId?: string
  private readonly runnableConfig: RunnableConfig

  constructor(params?: LLMInitParams) {
    // ✅ Step 1: 标准化 meta
    const meta = {
      ...(params?.meta ?? {}),
      ...(params?.promptName ? { promptName: params.promptName } : {}),
      ...(params?.meta?.name ? { promptName: params.meta.name } : {}),
      ...(params?.meta?.chat_id ? { thread_id: params.meta.chat_id } : {})
    }

    // ✅ Step 2: 设置 LangSmith 环境变量（项目名优先 params，其次配置）
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName
    process.env.LANGCHAIN_TRACING_V2 = 'true'
    process.env.LANGCHAIN_TRACING_V2 = 'true'
    if (params?.projectName) {
      process.env.LANGCHAIN_PROJECT = params.projectName
    }

    if (!params) {
      params = {}
    }

    // ✅ Step 3: 字段赋值（使用 ?? 提供默认值）
    this.model = params?.model ?? 'gpt-4.1'
    this.temperature = params?.temperature ?? 0
    this.maxTokens = params?.maxTokens ?? 1024
    this.responseJSON = params?.responseJSON
    this.systemPrompt = params?.systemPrompt

    // ✅ Step 4: 处理 runnableConfig 与元数据
    this.runnableConfig = params?.runnableConfig ?? {}
    this.runnableConfig.metadata = meta

    // ✅ Step 5: 提取 runId
    this.runId = params?.meta?.round_id
  }

  private createClient(type: ClientType) {
    switch (type) {
      case ClientType.AZURE:
        return AzureOpenAIClient.getClient({
          model: this.model,
          temperature: this.temperature,
          maxTokens: this.maxTokens
        })
      case ClientType.OPENAI:
        return OpenAIClient.getClient({
          model: this.model,
          temperature: this.temperature
        })
      case ClientType.CHEAP:
        return CheapOpenAI.getClient(this.model, this.temperature)
      case ClientType.QWEN:
        return QwenMax.getClient(this.temperature)
      default:
        console.warn('Unknown client type:', type)
        // 保底按照 Azure OpenAI
        return AzureOpenAIClient.getClient({
          model: this.model,
          temperature: this.temperature,
          maxTokens: this.maxTokens
        })
    }
  }

  /**
   * 根据 model 参数调整调用优先级
   * @private
   */
  // 定义客户端类型枚举

  private getClients() {
    // 根据条件确定客户端顺序
    let clientOrder: ClientType[]

    if (!Config.setting.localTest && this.model.startsWith('gpt')) {
      clientOrder = [ClientType.AZURE, ClientType.CHEAP, ClientType.OPENAI, ClientType.QWEN]
    } else if (this.model.startsWith('claude')) {
      clientOrder = [ClientType.CHEAP, ClientType.AZURE, ClientType.OPENAI, ClientType.QWEN]
    } else if (this.model.startsWith('qwen-max')) {
      clientOrder = [ClientType.QWEN, ClientType.AZURE, ClientType.OPENAI, ClientType.CHEAP]
    } else if (Config.setting.localTest) {
      clientOrder = [ClientType.CHEAP, ClientType.AZURE, ClientType.OPENAI, ClientType.QWEN]
    } else { // 保底配置
      clientOrder = [ClientType.AZURE, ClientType.OPENAI, ClientType.CHEAP, ClientType.QWEN]
    }

    // 按确定的顺序返回客户端
    return clientOrder.map(this.createClient.bind(this))
  }

  /**
   * 使用文本或 PromptTemplate 调用 LLM
   * LLM.predict('Hello, World!')
   * LLM.predict(PromptTemplate.from('Hello, {name}!'), { name: 'World' })
   * @param text 文本 或 PromptTemplate, 如果是 PromptTemplate，则使用 params 参数替换模板中的变量
   * @param promptParams
   */
  async predict<T extends string>(
    text: string | SystemMessagePromptTemplate<ParamsFromFString<T>> | Runnable,
    promptParams?: ParamsFromFString<T>
  ): Promise<string> {
    const clients = this.getClients()
    const parser = new StringOutputParser()

    for (const client of clients) {
      try {
        const runnableConfig = this.responseJSON ? {
          ...this.runnableConfig, response_format: { type: 'json_object' }
        } : this.runnableConfig

        if (typeof text === 'string') {
          return await client
            .pipe(parser)
            .withConfig(runnableConfig)
            .invoke(text, { runId: this.runId })
        } else if (promptParams) {
          return await text
            .pipe(client)
            .pipe(parser)
            .withConfig(runnableConfig)
            .invoke(promptParams, { runId: this.runId })
        }
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)

        if (clients.indexOf(client) === clients.length - 1) {
          throw e
        }
      }
    }
    throw new Error('All attempts to predict with available clients have failed.')
  }

  /**
   * 简单使用单条文本调用 OpenAI
   */
  public static async predict<T extends string>(
    text: string | SystemMessagePromptTemplate<ParamsFromFString<T>> | Runnable,
    params?: LLMInitParams,
    promptParams?: ParamsFromFString<T>
  ): Promise<string> {
    const llm = new LLM(params)
    return await llm.predict(text, promptParams) as string
  }

  private isMessagesContainSystemPrompt(messages: BaseMessage[]): boolean {
    return messages.length > 0 && messages[0]._getType() === 'system'
  }

  /**
   * 使用一组消息或 ChatPromptTemplate 调用 LLM，需要自己维护聊天记录
   * const messages = [
   *   new SystemMessage("You are a helpful assistant"),
   *   new HumanMessage("Tell me a joke about {topic}"),
   * ]
   * LLM.predictMessage(messages)
   *
   * const promptTemplate = ChatPromptTemplate.fromMessages([
   *   ["system", "You are a helpful assistant"],
   *   ["user", "Tell me a joke about {topic}"],
   * ])
   * LLM.predictMessage(promptTemplate, {topic: 'fuck'})
   * @param messages
   * @param params
   */
  async predictMessage<T extends BaseMessage [] | ChatPromptTemplate<any>>(messages: T, params?: T extends ChatPromptTemplate<infer P> ? P : never): Promise<string> {
    const chatHistory = messages

    const parser = new StringOutputParser()

    const clients =  this.getClients()
    for (const client of clients) {
      try {
        if (Array.isArray(chatHistory)) {
          return await client.pipe(parser).withConfig(this.runnableConfig).invoke(chatHistory, { runId: this.runId })
        } else {
          return await (messages as ChatPromptTemplate).pipe(client).pipe(parser).withConfig(this.runnableConfig).invoke(params, { runId: this.runId })
        }
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)

        if (clients.indexOf(client) === clients.length - 1) {
          throw e
        }
      }
    }
    throw new Error('All attempts to predict with available clients have failed.')
  }

  /**
   * 使用一组消息调用 OpenAI，需要自己维护聊天记录
   * @param messages
   * @param params
   */
  public static async predictMessage(messages: BaseMessage[], params?: LLMInitParams): Promise<string> {
    const llm = new LLM(params)
    return await llm.predictMessage(messages) as string
  }

  /**
   * 流式输出，返回一个可迭代的流，包含多个 string 输出
   * 例如：
   * for await (const chunk of stream) {
   *   console.log(chunk);
   * }
   *
   * 输出：
   *   Hello
   *   !
   *   How
   *   can
   *   I
   *   assist
   *   you
   *   today
   *   ?
   *   参考： https://js.langchain.com/docs/modules/model_io/models/chat/how_to/streaming
   */
  async stream(messages: BaseMessage[], systemPrompt?: string): Promise<IterableReadableStream<string>> {
    let chatHistory = this.cleanPromptMessages(messages)
    if (systemPrompt) {
      chatHistory = [new SystemMessage(systemPrompt), ...messages]
    } else if (this.systemPrompt && !this.isMessagesContainSystemPrompt(messages)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...messages]
    }
    const parser = new StringOutputParser()

    const clients = this.getClients()
    for (const client of clients) {
      try {
        return await client.pipe(parser).withConfig(this.runnableConfig).stream(chatHistory, { runId: this.runId })
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)
      }
    }

    throw new Error('All attempts to stream with available clients have failed.')
  }

  cleanPromptMessage(prompt: string) {
    return StringHelper.replaceMultipleBlankLines(prompt).trim()
  }

  async imageChat(imageUrl: string, systemPrompt?: string) {
    const message = new HumanMessage({
      content: [
        { type: 'image_url', image_url: { url: imageUrl } }
      ]
    })
    let chatHistory: BaseMessage[] = [message]
    if (systemPrompt) {
      chatHistory = [new SystemMessage(systemPrompt), ...chatHistory]
    } else if (this.systemPrompt && !this.isMessagesContainSystemPrompt(chatHistory)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...chatHistory]
    }
    const parser = new StringOutputParser()
    const clients = this.getClients()
    for (const client of clients) {
      try {
        return await client.pipe(parser).withConfig(this.runnableConfig).invoke(chatHistory, { runId: this.runId })
      } catch (e) {
        console.error('Error summarizing image:', e)
        throw new Error('Failed to summarize image.')
      }
    }
  }

  /**
   * Prompt 格式化，移除无用空行
   * @param messages
   * @private
   */
  private cleanPromptMessages(messages: BaseMessage[]) {
    messages.forEach((message) => {
      if (typeof message.content === 'string') {
        message.content = this.cleanPromptMessage(message.content)
      }
    })

    return messages
  }

  static async sendMsg(line: string, chat_id: string, user_id: string, round_id: string, noSplit?: boolean) {
    if (noSplit) {
      // 直接把文件移除掉，进行输出
      line = line.replaceAll(/\[[^\]]*]/g, '')

      return await MessageSender.sendById ({
        chat_id: chat_id,
        user_id: user_id,
        ai_msg: line
      }, { round_id: round_id })
    } else {
      // 将文件部分提取出来，放到文本后面。
      const parts = line.split(/\[((?:.*?)_(?:\w{4})\.(?:\w+))\]/)
      const fileRegex =   /.*?_\w{4}\.\w+/

      let textPart = ''
      const filePart: string[] = []

      for (let part of parts) {
        if (fileRegex.test(part)) {
          filePart.push(part)
        } else {
          if (part.endsWith('：') || part.endsWith(':')) {
            part = part.replace(/[：:]/, '')
          }

          textPart += part
        }
      }

      textPart = textPart.replaceAll(/\s+/g, ' ').replaceAll(/\[[^\]]*]/g, '').trim()

      if (textPart) {
        await MessageSender.sendById ({
          chat_id: chat_id,
          user_id: user_id,
          ai_msg: textPart
        }, { round_id: round_id })
      }

      for (const fileString of filePart) {
        // 这是一个文件名
        const description = fileString.split('_')[0]
        const extension = PathHelper.getFileExt(fileString)
        const fileName = fileString

        // 构建消息对象
        let message

        const fileUrl = `https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/rag_file/${encodeURIComponent(fileName)}`
        const [err, res]  = await catchError(FileHelper.getFileSizeFromUrl(fileUrl))
        if (err || (res && res === 0)) {
          // 文件不存在，有可能是模型幻觉，或者文件被删除了
          logger.error(`${fileName}文件不存在`)
          continue
        }

        // 根据文件后缀名判断文件类型
        switch (extension.toLowerCase ()) {
          case 'jpg':
          case 'jpeg':
          case 'png':
          case 'webp':
            message = {
              type: IWecomMsgType.Image,
              url: fileUrl
            }
            break
          case 'mp4':
          case 'avi':
          case 'mov':
          case 'wmv':
          case 'flv':
          case 'mkv':
            message = {
              type: IWecomMsgType.Video,
              url: fileUrl
            }
            break
          default:
            message = {
              type: IWecomMsgType.File,
              name: description,
              url: fileUrl
            }
        }

        await randomSleep(3000, 5000)

        // 发送文件消息
        await MessageSender.sendById ({
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: `[${description}]`,
          send_msg: message,
        }, {
          shortDes: `[${description}]`,
          round_id: round_id
        })
      }
    }

  }

  /**
   * 通用工具调用封装函数
   * @param tools 工具数组，每个工具都是使用 LangChain tool() 函数创建的
   * @param userMessage 客户消息
   * @param systemMessage 可选的系统消息，如果提供会放到消息列表最前面
   * @returns 工具调用结果的拼接字符串
   */
  async toolCall(
    tools: StructuredTool[],
    userMessage: string,
    systemMessage?: string
  ): Promise<string> {
    try {
      // 构建消息数组
      const messages: BaseMessage[] = []

      // 如果有系统消息，放到最前面
      if (systemMessage) {
        messages.push(new SystemMessage(systemMessage))
      }

      // 添加客户消息
      messages.push(new HumanMessage(userMessage))

      // 获取客户端并绑定工具
      const clients = this.getClients()
      let response: AIMessage | null = null
      let lastError: any = null

      // 尝试不同的客户端
      for (const client of clients) {
        try {
          if (client.bindTools) {
            const modelWithTools = client.bindTools(tools)
            response = await modelWithTools.withConfig(this.runnableConfig).invoke(messages, { runId: this.runId })
          }
          break
        } catch (e) {
          lastError = e
          logger.log(`Tool call failed with client ${clients.indexOf(client)}: ${e}`)
          if (clients.indexOf(client) === clients.length - 1) {
            throw e
          }
        }
      }

      if (!response) {
        throw lastError || new Error('All tool call attempts failed')
      }

      // 检查是否有工具调用
      if (!response.tool_calls || response.tool_calls.length === 0) {
        logger.log('No tool calls detected in response', response.content)
        return ''
      }

      // 打印需要调用的工具信息
      logger.log(`检测到 ${response.tool_calls.length} 个工具调用:`)
      response.tool_calls.forEach((toolCall: ToolCall, index: number) => {
        console.log(`${index + 1}. 函数名: ${toolCall.name}  参数: ${JSON.stringify(toolCall.args)}`)
      })

      // 执行工具调用并收集结果
      const toolResults: string[] = []

      for (const toolCall of response.tool_calls) {
        try {
          // 查找对应的工具
          const tool = tools.find((t) => t.name === toolCall.name)

          if (!tool) {
            const errorMsg = `未找到名为 "${toolCall.name}" 的工具`
            logger.log(errorMsg)
            toolResults.push(errorMsg)
            continue
          }

          // 调用工具函数
          logger.log(`正在调用工具: ${toolCall.name}`)
          const result = await tool.invoke(toolCall.args)

          // 确保结果是字符串格式
          let resultString: string
          if (typeof result === 'string') {
            resultString = result
          } else if (result === null || result === undefined) {
            resultString = ''
          } else {
            resultString = JSON.stringify(result, null, 2)
          }

          toolResults.push(resultString)
          logger.log(`工具 "${toolCall.name}" 调用完成`)

        } catch (error) {
          const errorMsg = ''
          logger.warn(`工具 ${toolCall.name} 调用失败: ${error}`)
          toolResults.push(errorMsg)
        }
      }

      // 拼接所有工具调用结果
      return toolResults.join('\n\n')
    } catch (error) {
      logger.log(`工具调用过程发生错误: ${error}`)

      return ''
    }
  }

  /**
   * 静态方法：通用工具调用封装函数
   * @param tools 工具数组，每个工具都是使用 LangChain tool() 函数创建的
   * @param userMessage 客户消息
   * @param systemMessage 可选的系统消息，如果提供会放到消息列表最前面
   * @param params LLM初始化参数
   * @returns 工具调用结果的拼接字符串
   */
  public static async toolCall(
    tools: StructuredTool[],
    userMessage: string,
    systemMessage?: string,
    params?: LLMInitParams
  ): Promise<string> {
    const llm = new LLM(params)
    return await llm.toolCall(tools, userMessage, systemMessage)
  }
}